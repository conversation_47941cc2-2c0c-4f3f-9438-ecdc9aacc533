@extends('layouts.app')

@section('title', 'หน้าหลัก - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@push('styles')
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.body.classList.add('hero-page');

    // Enhanced navbar scroll effect for hero page
    const navbar = document.querySelector('.navbar');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
});
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ $restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center;">
        <div class="container-fluid h-100">
            <div class="row h-100 align-items-center justify-content-center text-center">
                <div class="col-lg-10 col-xl-8">
                    <div class="hero-content text-white fade-in-up">
                        <h1 class="hero-title display-2 fw-bold mb-4 text-shadow">
                            <i class="fas fa-anchor me-3 text-warning pulse-animation"></i>
                            ร้านก๋วยเตี๋ยวเรือเข้าท่า
                        </h1>
                        <p class="hero-subtitle lead mb-4 fs-3 mx-auto" style="max-width: 900px; line-height: 1.6;">
                            {{ $restaurantInfo->description ?? 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง' }}
                        </p>
                        <p class="hero-tagline mb-5 fs-5 opacity-90" style="max-width: 700px; margin: 0 auto;">
                            {{ $restaurantInfo->tagline ?? 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย' }}
                        </p>

                        <!-- Decorative Line -->
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-utensils text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden" style="z-index: 1;">
        <div class="position-absolute" style="top: 10%; left: -5%; width: 200px; height: 200px; background: rgba(255, 193, 7, 0.1); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div class="position-absolute" style="top: 60%; right: -5%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div class="position-absolute" style="bottom: 20%; left: 10%; width: 100px; height: 100px; background: rgba(139, 69, 19, 0.2); border-radius: 50%; animation: float 7s ease-in-out infinite;"></div>
    </div>

    <!-- Wave Pattern -->
    <div class="position-absolute bottom-0 start-0 w-100" style="z-index: 2;">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" style="height: 60px; width: 100%;">
            <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="rgba(255,255,255,0.3)"></path>
            <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="rgba(255,255,255,0.2)"></path>
            <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="rgba(255,255,255,0.1)"></path>
        </svg>
    </div>
</section>

    <!-- Scroll Indicator -->
    <div class="position-absolute bottom-0 start-50 translate-middle-x mb-4" style="z-index: 10;">
        <div class="text-white text-center">
            <small class="d-block mb-2">เลื่อนลงเพื่อดูเพิ่มเติม</small>
            <i class="fas fa-chevron-down fa-2x animate-bounce"></i>
        </div>
    </div>
</section>

<style>
/* Enhanced Hero Section Styles */
.hero-section {
    height: 100vh;
    min-height: 600px;
}

.hero-slide {
    height: 100vh;
    min-height: 600px;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed;
    filter: brightness(0.8) contrast(1.1) saturate(1.2);
    display: flex;
    align-items: center;
    position: relative;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.5) 0%,
        rgba(139, 69, 19, 0.7) 30%,
        rgba(218, 165, 32, 0.4) 70%,
        rgba(0, 0, 0, 0.6) 100%
    );
    z-index: 1;
}

/* Hero Overlay for Better Text Readability */
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(139, 69, 19, 0.6) 50%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 1;
}

/* Hero Content Card */
.hero-content-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 3rem 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}



/* Restaurant Title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
}

.restaurant-name {
    color: #ffffff;
    display: block;
}

.restaurant-highlight {
    color: #ffc107;
    display: block;
    font-size: 0.9em;
    margin-top: 0.5rem;
}

/* Hero Description */
.hero-description {
    margin: 2rem 0;
}

.lead-text {
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 1.6;
    color: #ffffff;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1rem;
}

.sub-text {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Hero Action Buttons */
.hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.btn-hero-primary {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    border: none;
    color: #000;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 193, 7, 0.6);
    color: #000;
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: #ffffff;
}

/* Enhanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Text Shadows */
.text-shadow {
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.5),
        0 0 10px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 0, 0, 0.2);
}

/* Fade In Animation */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

/* Button Hover Effects */
.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

/* Glass Effect Enhancement */
.hero-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: 25px;
    pointer-events: none;
}







.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.animate-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Additional Styles for Enhanced Hero */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Button Styles */
.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
    }

    .hero-slide {
        height: 70vh;
        min-height: 500px;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .fs-3 {
        font-size: 1.2rem !important;
    }
}

</style>

<!-- Featured Menu Section -->
<section class="py-4 bg-white position-relative" style="margin-top: 1rem; padding-top: 1rem !important;">
    <div class="container">


        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-5 fw-bold text-primary mb-3">
                        <i class="fas fa-star text-warning me-2"></i>เมนูแนะนำ
                    </h2>
                    <p class="lead text-muted mt-4 fs-4">เมนูยอดนิยมที่ลูกค้าแนะนำ</p>
                    <div class="d-inline-block">
                        <div class="bg-warning" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        @if($featuredMenus->count() > 0)
            <!-- Debug: Found {{ $featuredMenus->count() }} featured menus -->
            <div class="row g-4">
                @foreach($featuredMenus as $index => $menu)
                    <!-- Debug: Menu {{ $index + 1 }}: {{ $menu->name }}, Image: {{ $menu->image ? 'Yes (' . $menu->image . ')' : 'No' }} -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-0 shadow-lg position-relative overflow-hidden" style="animation-delay: {{ $index * 0.1 }}s;">
                            <!-- Featured Badge -->
                            <div class="position-absolute top-0 end-0 z-index-1">
                                <div class="bg-warning text-dark px-3 py-2 rounded-bottom-start">
                                    <i class="fas fa-crown me-1"></i>แนะนำ
                                </div>
                            </div>

                            @if($menu->image)
                                @php
                                    $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($menu->image);
                                @endphp
                                <div class="position-relative overflow-hidden"
                                     style="cursor: pointer;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     data-image-src="{{ $imageUrl }}"
                                     data-image-alt="{{ $menu->name }}">
                                    <img src="{{ $imageUrl }}"
                                         class="card-img-top featured-menu-image"
                                         alt="{{ $menu->name }}"
                                         loading="eager"
                                         onerror="console.log('Image failed to load:', this.src); this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                         onload="console.log('Image loaded successfully:', this.src);"
                                         style="height: 250px; object-fit: cover; transition: transform 0.3s ease; opacity: 1 !important; visibility: visible !important; display: block !important;">
                                    <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25" style="pointer-events: none;"></div>
                                    <!-- Fallback placeholder (hidden by default) -->
                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-light position-absolute top-0 start-0 w-100 h-100" style="height: 250px; display: none;">
                                    </div>
                                </div>
                            @else
                                <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center position-relative"
                                     style="height: 250px; background: linear-gradient(135deg, #f8f9fa, #e9ecef);">
                                    <img src="{{ asset('images/menu/placeholder.svg') }}"
                                         alt="ไม่มีรูปภาพ"
                                         class="opacity-75"
                                         style="width: 150px; height: 150px; object-fit: contain;">
                                </div>
                            @endif

                            <div class="card-body d-flex flex-column p-4">
                                <div class="text-center mb-3">
                                    <h5 class="card-title text-primary fw-bold mb-2">{{ $menu->name }}</h5>
                                    <span class="badge bg-primary rounded-pill">{{ $menu->category->name }}</span>
                                </div>

                                <p class="card-text text-muted flex-grow-1 mb-3">{{ $menu->description }}</p>

                                <div class="d-flex justify-content-center align-items-center mt-auto">
                                    <div class="price-tag">
                                        <span class="h4 text-primary fw-bold mb-0">{{ $menu->formatted_price }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Hover Effect -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 transition-opacity"></div>
                        </div>
                    </div>
                @endforeach
            </div>


        @else
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-utensils fa-5x text-muted opacity-50"></i>
                </div>
                <h4 class="text-muted mb-3">ยังไม่มีเมนูแนะนำ</h4>
                <p class="text-muted">เรากำลังเตรียมเมนูพิเศษสำหรับคุณ</p>
                @auth
                    @if(Auth::user()->isAdmin())
                        <a href="{{ route('admin.menu-items.create') }}" class="btn btn-primary mt-3">
                            <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                        </a>
                    @endif
                @endauth
            </div>
        @endif
    </div>
</section>

<style>
.card:hover .card-img-top {
    transform: scale(1.05);
}

.card:hover .transition-opacity {
    opacity: 1 !important;
}

.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color));
    border-radius: 1px;
}

.z-index-1 {
    z-index: 1;
}
</style>

<!-- Categories Section -->
<section class="py-5 position-relative" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="fade-in-up">
                    <h2 class="section-title display-5 fw-bold text-primary mb-3">
                        <i class="fas fa-th-large text-warning me-2"></i>หมวดหมู่อาหาร
                    </h2>
                    <p class="lead text-muted mt-4 fs-4">เลือกหมวดหมู่ที่คุณต้องการ พร้อมเมนูหลากหลายรสชาติ</p>
                    <div class="d-inline-block">
                        <div class="bg-primary" style="height: 3px; width: 80px; border-radius: 2px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            @foreach($categories->where('slug', '!=', 'recommended') as $index => $category)
                <div class="col-lg-3 col-md-6 mb-4">
                    <a href="{{ route('menu.category', $category->slug) }}" class="text-decoration-none">
                        <div class="card text-center h-100 border-0 shadow-lg category-card position-relative overflow-hidden"
                             style="animation-delay: {{ $index * 0.1 }}s;">

                            <!-- Category Icon Overlay -->
                            <div class="position-absolute top-0 end-0 p-3 z-index-1">
                                <div class="bg-white bg-opacity-90 rounded-circle p-2 shadow-sm">
                                    @if($category->icon)
                                        @if(str_contains($category->icon, 'icon-boat-noodle'))
                                            <span class="{{ $category->icon }}"></span>
                                        @else
                                            <i class="{{ $category->icon }} text-primary"></i>
                                        @endif
                                    @else
                                        <span class="icon-boat-noodle"></span>
                                    @endif
                                </div>
                            </div>

                            @if($category->image)
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ asset('storage/' . $category->image) }}"
                                         class="card-img-top category-image"
                                         alt="{{ $category->name }}"
                                         style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 start-0 w-100 h-50 bg-gradient-to-top"></div>
                                </div>
                            @else
                                <div class="card-img-top d-flex align-items-center justify-content-center position-relative"
                                     style="height: 180px; background: linear-gradient(135deg, #007bff, #6c757d);">
                                    @if($category->icon)
                                        @if(str_contains($category->icon, 'icon-boat-noodle'))
                                            <img src="{{ asset('images/menu/mixed-noodle.svg') }}" alt="{{ $category->name }}" style="width: 80px; height: 80px; opacity: 0.8;">
                                        @else
                                            <i class="{{ $category->icon }} fa-4x text-white opacity-75"></i>
                                        @endif
                                    @else
                                        <img src="{{ asset('images/menu/mixed-noodle.svg') }}" alt="ไม่มีรูปภาพ" style="width: 80px; height: 80px; opacity: 0.8;">
                                    @endif
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3">
                                        <small class="text-white opacity-75">{{ $category->name }}</small>
                                    </div>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <small class="text-white opacity-75"><i class="fas fa-image"></i></small>
                                    </div>
                                </div>
                            @endif

                            <div class="card-body d-flex flex-column p-4">
                                <h6 class="card-title text-primary fw-bold mb-3">{{ $category->name }}</h6>
                                <p class="card-text text-muted flex-grow-1 mb-3">{{ $category->description }}</p>

                                <div class="mt-auto">

                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-utensils me-1"></i>
                                            {{ $category->menuItems->count() }} รายการ
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Hover Overlay -->
                            <div class="position-absolute top-0 start-0 w-100 h-100 bg-primary bg-opacity-10 opacity-0 hover-overlay"></div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
</section>

<style>
.category-card {
    transition: all 0.4s ease;
    transform: translateY(0);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.category-card:hover .category-image {
    transform: scale(1.1);
}

.category-card:hover .hover-overlay {
    opacity: 1 !important;
}

.category-card:hover .arrow-icon {
    transform: translateX(5px);
}

.category-image {
    transition: transform 0.4s ease;
}

.arrow-icon {
    transition: transform 0.3s ease;
}

.category-btn {
    transition: all 0.3s ease;
}

.category-card:hover .category-btn {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.bg-gradient-to-top {
    background: linear-gradient(to top, rgba(0,0,0,0.3), transparent);
}

.hover-overlay {
    transition: opacity 0.3s ease;
}

.z-index-1 {
    z-index: 1;
}
</style>


<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow-lg" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
/* Enhanced Homepage Styles */
.hero-slide {
    position: relative;
    overflow: hidden;
}

.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><path d="M0,10 Q25,0 50,10 T100,10 L100,20 L0,20 Z" fill="%23ffffff" opacity="0.1"/></svg>');
    background-size: 200px 20px;
    background-repeat: repeat-x;
    background-position: bottom;
    pointer-events: none;
}

.text-shadow {
    text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
}

.hover-shine {
    transition: opacity 0.3s ease;
}

.btn:hover .hover-shine {
    opacity: 0.1 !important;
}

/* Enhanced Card Animations */
.card {
    transform: translateY(20px);
    opacity: 0;
    animation: cardFadeIn 0.6s ease forwards;
}

@keyframes cardFadeIn {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Image Modal Styles */
#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
}

/* Price Tag Enhancement */
.price-tag {
    position: relative;
}

.price-tag::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -10px;
    right: -10px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--cream-color), var(--light-cream));
    border-radius: var(--border-radius-sm);
    z-index: -1;
    opacity: 0.7;
}

/* Section Dividers */
.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--gold-color), var(--primary-color));
    border-radius: 2px;
}



/* Force smaller hero title */
.hero-title.display-4 {
    font-size: 2.5rem !important;
    font-family: 'Prompt', 'Kanit', sans-serif !important;
    font-weight: 600 !important;
}

/* Fix Featured Menu Section Title */
.section-title.h2 {
    font-size: 1.8rem !important;
    margin-bottom: 1.5rem !important;
    font-family: 'Prompt', 'Kanit', sans-serif !important;
    font-weight: 600 !important;
}

/* Ensure Featured Menu Section has enough space */
section[style*="margin-top"] {
    margin-top: 4rem !important;
    padding-top: 3rem !important;
}

/* Additional safety margin for featured menu section */
.bg-white.position-relative {
    margin-top: 4rem !important;
    padding-top: 3rem !important;
}

/* Prevent text overflow to topbar */
.section-title {
    position: relative;
    z-index: 1;
    margin-top: 2rem !important;
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-content-card {
        padding: 2.5rem 1.5rem;
    }

    .lead-text {
        font-size: 1.3rem;
    }

    /* Featured Menu Section Responsive */
    .section-title.h2 {
        font-size: 1.6rem !important;
    }

    section[style*="margin-top"] {
        margin-top: 3rem !important;
        padding-top: 2rem !important;
    }
}

@media (max-width: 768px) {
    /* Hero Title Mobile */
    .hero-title {
        font-size: 2rem !important;
    }

    /* Featured Menu Section Mobile */
    .section-title.h2 {
        font-size: 1.4rem !important;
        margin-bottom: 1rem !important;
    }

    /* Add more margin on mobile to avoid topbar overlap */
    section[style*="margin-top"] {
        margin-top: 4rem !important;
        padding-top: 3rem !important;
    }
}

@media (max-width: 576px) {
    /* Hero Title Small Mobile */
    .hero-title {
        font-size: 1.75rem !important;
    }

    /* Featured Menu Section Small Mobile */
    .section-title.h2 {
        font-size: 1.2rem !important;
    }

    section[style*="margin-top"] {
        margin-top: 5rem !important;
        padding-top: 4rem !important;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
    }

    .hero-slide {
        min-height: 70vh;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-content-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .lead-text {
        font-size: 1.2rem;
    }

    .sub-text {
        font-size: 1rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.8rem 2rem;
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        gap: 0.8rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        min-height: 60vh;
    }

    .hero-slide {
        min-height: 60vh;
    }

    .hero-title {
        font-size: 2rem;
    }

    .restaurant-name {
        font-size: 1em;
    }

    .restaurant-highlight {
        font-size: 0.8em;
    }

    .hero-content-card {
        padding: 1.5rem 1rem;
        border-radius: 15px;
    }

    .lead-text {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .sub-text {
        font-size: 0.95rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.7rem 1.5rem;
        font-size: 0.95rem;
        width: 100%;
        max-width: 280px;
    }


}

/* Force Featured Menu Images Visibility - แก้ปัญหารูปหายเมื่อเลื่อนหน้า */
.featured-menu-image,
.card-img-top,
.category-image {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Override any lazy loading or intersection observer effects */
img[loading="lazy"],
img[data-src],
.lazy-load,
.fade-in,
.fade-out {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    transform: none !important;
}

/* Ensure featured menu cards are always visible */
.boat-menu-card,
.card {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

</style>
@endpush

@push('scripts')
<script>
// Force page refresh if user just registered
@if(session('user_registered') || session('force_refresh'))
    // Small delay to ensure session is properly set
    setTimeout(function() {
        // Force a hard refresh to ensure navigation updates
        window.location.href = window.location.href;
    }, 100);
@endif

// Force image visibility - แก้ปัญหารูปหายเมื่อเลื่อนหน้า
function ensureFeaturedImagesVisible() {
    // Force all images to be visible
    const images = document.querySelectorAll('.featured-menu-image, .card-img-top, .category-image, img');
    images.forEach(function(img) {
        img.style.opacity = '1';
        img.style.visibility = 'visible';
        img.style.display = 'block';
        img.style.position = 'relative';
        img.style.zIndex = '1';
        img.style.transform = 'none';
        img.style.transition = 'transform 0.3s ease';
    });

    // Ensure cards are visible
    const cards = document.querySelectorAll('.boat-menu-card, .card');
    cards.forEach(function(card) {
        card.style.opacity = '1';
        card.style.visibility = 'visible';
        card.style.display = 'block';
        card.style.transform = 'none';
    });

    // Remove any fade classes that might hide elements
    const fadeElements = document.querySelectorAll('.fade-out, .hidden, .invisible');
    fadeElements.forEach(function(element) {
        element.classList.remove('fade-out', 'hidden', 'invisible');
        element.style.opacity = '1';
        element.style.visibility = 'visible';
        element.style.display = 'block';
    });
}

// Also check if we need to update navigation after any auth change
document.addEventListener('DOMContentLoaded', function() {
    // Force images to be visible immediately
    ensureFeaturedImagesVisible();

    // Run again after a short delay
    setTimeout(ensureFeaturedImagesVisible, 100);

    // Run on scroll to ensure images stay visible
    window.addEventListener('scroll', ensureFeaturedImagesVisible);

    // Run on resize
    window.addEventListener('resize', ensureFeaturedImagesVisible);

    // Run periodically to ensure images stay visible
    setInterval(ensureFeaturedImagesVisible, 1000);

    @if(session('user_registered'))
        // Additional check to ensure navigation is updated
        setTimeout(function() {
            const navElement = document.querySelector('.navbar-nav');
            if (navElement && navElement.textContent.includes('Not logged in')) {
                location.reload();
            }
        }, 200);
    @endif

    // Handle image modal
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    if (imageModal && modalImage) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
        });

        // Close modal when clicking on the image
        modalImage.addEventListener('click', function() {
            const modalInstance = bootstrap.Modal.getInstance(imageModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(imageModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }

    // Add click listeners to all menu images
    document.querySelectorAll('[data-bs-target="#imageModal"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            const imageSrc = this.getAttribute('data-image-src');
            const imageAlt = this.getAttribute('data-image-alt');

            // Set modal image
            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;

            // Show modal
            const modal = new bootstrap.Modal(imageModal);
            modal.show();
        });
    });
});
</script>
@endpush
