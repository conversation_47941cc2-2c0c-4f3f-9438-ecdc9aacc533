<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - ร้านก๋วยเตี๋ยวเรือเข้าท่า</title>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --accent-color: #FFD700;
            --boat-blue: #4682B4;
            --cream-color: #FFF8DC;
            --text-dark: #2C1810;
            --text-light: #6B4423;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--boat-blue) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="boat" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M10,30 Q25,20 40,30 L35,35 Q25,25 15,35 Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23boat)"/></svg>') repeat;
            opacity: 0.3;
            z-index: 1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 2;
            border: 2px solid rgba(255, 215, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-container .header h1 {
            font-family: 'Kanit', sans-serif;
            color: var(--primary-color) !important;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: none;
        }

        .login-container .header .subtitle {
            color: var(--text-light) !important;
            font-size: 1rem;
            font-weight: 400;
            text-shadow: none;
        }



        .boat-icon {
            color: var(--accent-color);
            font-size: 2.5rem;
            margin-bottom: 15px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .form-group {
            margin: 20px 0;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 500;
            font-size: 0.95rem;
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            z-index: 1;
        }

        input {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Sarabun', sans-serif;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
            background: white;
        }

        .btn-login {
            width: 100%;
            padding: 15px 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Sarabun', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
            margin-top: 10px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            padding: 12px 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
            font-weight: 500;
        }

        .success {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            padding: 12px 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            font-weight: 500;
        }

        .divider {
            margin: 30px 0;
            text-align: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, var(--text-light), transparent);
        }

        .divider span {
            background: white;
            padding: 0 20px;
            color: var(--text-light);
            font-weight: 500;
        }

        .debug-info {
            margin: 20px 0;
        }

        .info-card {
            background: rgba(139, 69, 19, 0.05);
            border: 1px solid rgba(139, 69, 19, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-card h4 {
            color: var(--primary-color);
            font-family: 'Kanit', sans-serif;
            font-size: 1.1rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .info-card p {
            margin: 8px 0;
            color: var(--text-dark);
            font-size: 0.95rem;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-badge.logged-in {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-badge.logged-out {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .admin-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .admin-badge.is-admin {
            background: rgba(255, 215, 0, 0.2);
            color: var(--primary-color);
        }

        .admin-badge.not-admin {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }

        code {
            background: rgba(139, 69, 19, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            color: var(--primary-color);
        }

        .quick-links h4 {
            color: var(--primary-color);
            font-family: 'Kanit', sans-serif;
            font-size: 1.1rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .link-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            border-radius: 12px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .link-btn i {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .link-btn.home {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .link-btn.home:hover {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
            transform: translateY(-2px);
        }

        .link-btn.login {
            background: rgba(0, 123, 255, 0.1);
            color: #007bff;
        }

        .link-btn.login:hover {
            background: rgba(0, 123, 255, 0.2);
            border-color: #007bff;
            transform: translateY(-2px);
        }

        .link-btn.admin {
            background: rgba(255, 215, 0, 0.2);
            color: var(--primary-color);
        }

        .link-btn.admin:hover {
            background: rgba(255, 215, 0, 0.3);
            border-color: var(--accent-color);
            transform: translateY(-2px);
        }

        .logout-form {
            margin: 0;
        }

        .link-btn.logout {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: none;
            cursor: pointer;
            font-family: 'Sarabun', sans-serif;
            width: 100%;
        }

        .link-btn.logout:hover {
            background: rgba(220, 53, 69, 0.2);
            border-color: #dc3545;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }

            .header h1 {
                font-size: 1.6rem;
            }

            .boat-icon {
                font-size: 2rem;
            }

            .links-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="header">
            <div class="boat-icon">
                <i class="fas fa-anchor"></i>
            </div>
            <h1>ร้านก๋วยเตี๋ยวเรือเข้าท่า</h1>
            <p class="subtitle">เข้าสู่ระบบจัดการร้าน</p>
        </div>

        <?php if(session('success')): ?>
            <div class="success">
                <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="error">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div><?php echo e($error); ?></div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo e(route('simple.login.post')); ?>">
            <?php echo csrf_field(); ?>

            <div class="form-group">
                <label for="email">อีเมล</label>
                <div class="input-wrapper">
                    <i class="fas fa-envelope input-icon"></i>
                    <input type="email" id="email" name="email" value="<?php echo e(old('email', '<EMAIL>')); ?>" required placeholder="กรอกอีเมลของคุณ">
                </div>
            </div>

            <div class="form-group">
                <label for="password">รหัสผ่าน</label>
                <div class="input-wrapper">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" id="password" name="password" value="admin123" required placeholder="กรอกรหัสผ่าน">
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                </button>
            </div>
        </form>

        <div class="divider">
            <span>ข้อมูลระบบ</span>
        </div>

        <div class="debug-info">
            <div class="info-card">
                <h4><i class="fas fa-info-circle me-2"></i>สถานะการเข้าสู่ระบบ</h4>
                <p><strong>สถานะ:</strong> <span class="status-badge <?php echo e(Auth::check() ? 'logged-in' : 'logged-out'); ?>"><?php echo e(Auth::check() ? 'เข้าสู่ระบบแล้ว' : 'ยังไม่ได้เข้าสู่ระบบ'); ?></span></p>
                <?php if(Auth::check()): ?>
                    <p><strong>ผู้ใช้:</strong> <?php echo e(Auth::user()->name); ?> (<?php echo e(Auth::user()->email); ?>)</p>
                    <p><strong>บทบาท:</strong> <?php echo e(Auth::user()->role); ?></p>
                    <p><strong>ผู้ดูแลระบบ:</strong> <span class="admin-badge <?php echo e(Auth::user()->isAdmin() ? 'is-admin' : 'not-admin'); ?>"><?php echo e(Auth::user()->isAdmin() ? 'ใช่' : 'ไม่ใช่'); ?></span></p>
                <?php endif; ?>
                <p><strong>Session ID:</strong> <code><?php echo e(session()->getId()); ?></code></p>
            </div>
        </div>

        <div class="quick-links">
            <h4><i class="fas fa-link me-2"></i>ลิงก์ด่วน</h4>
            <div class="links-grid">
                <a href="<?php echo e(route('home')); ?>" class="link-btn home">
                    <i class="fas fa-home"></i>
                    <span>หน้าหลัก</span>
                </a>
                <a href="<?php echo e(route('login')); ?>" class="link-btn login">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>เข้าสู่ระบบปกติ</span>
                </a>
                <?php if(Auth::check() && Auth::user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="link-btn admin">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>แดชบอร์ดผู้ดูแล</span>
                    </a>
                <?php endif; ?>
                <?php if(Auth::check()): ?>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="logout-form">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="link-btn logout">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>ออกจากระบบ</span>
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>


</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\web แก้ไข\LastNoodle11111\resources\views/simple-login.blade.php ENDPATH**/ ?>